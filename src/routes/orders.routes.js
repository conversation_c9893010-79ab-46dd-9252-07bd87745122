import {
    getComplaint,
    addComplaint,
    myOrders,
    addFeed,
    checkOrder,
    validate<PERSON>oupon,
    trackYourOrder,
    getOrderByRefId,
} from "../controllers/orders.controllers.js";
import { verifyJWT } from "../middlewares/auth.middlewares.js";

export default async function orderRoutes(fastify, options) {
console.log('fastify :', fastify);
    fastify.get("/myOrders", { preHandler: verifyJWT }, myOrders);
    fastify.post("/addComplaint", addComplaint);
    fastify.post("/getComplaint", getComplaint);
    fastify.get(
        "/myOrder/trackdata",
        { preHandler: verifyJWT },
        trackYourOrder
    );
    fastify.post("/addFeed", addFeed);
    fastify.post("/checkOrder", checkOrder);
    fastify.get("orderByRefId", { preHandler: verifyJWT }, getOrderByRefId);
}

//my
// {preHandler: verifyJWT} removed for testing
