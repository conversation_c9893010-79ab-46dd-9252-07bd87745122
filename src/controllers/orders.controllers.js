import {
    getComplaintService,
    addComplaintService,
    myOrdersService,
    addFeedService,
    checkOrderService,
    checkCouponCode,
    // getAvailablePromotions,
    trackYourOrderService,
    getOrderByRefIdService,
} from "../services/orders.service.js";
import { successResponse, errorResponse } from "../utils/response.js"; // your response utils

// Coupon validation endpoint (moved from promotion.controllers.js)
export const validateCoupon = async (request, reply) => {
    try {
        console.log("validateCoupon called with body:", request.body);
        const { coupon, tamount } = request.body;

        // Check for missing or empty coupon and tamount
        if (
            !coupon ||
            coupon.trim() === "" ||
            !tamount ||
            tamount === "" ||
            isNaN(parseFloat(tamount))
        ) {
            console.log("Validation failed - missing required fields");
            return errorResponse(
                reply,
                400,
                "Missing or invalid required fields: coupon and tamount",
                {},
                "error",
                request
            );
        }

        if (!request.user || !request.user.user_id) {
            request.user = { user_id: 628042 };
        }

        const result = await checkCouponCode(request);
        const hasError = result.msg && result.msg !== "";
        const status = hasError ? "error" : "success";
        const statusCode = hasError ? 400 : 200;

        return successResponse(
            reply,
            statusCode,
            result.msg || "Coupon validated successfully",
            status,
            {
                discount: result.discount || 0,
                link: result.link || "",
                coupon_code: coupon,
                total_amount: parseFloat(tamount),
            }
        );
    } catch (error) {
        return errorResponse(reply, 500, error, {}, "error", request);
    }
};

export const getComplaint = async (req, reply) => {
    try {
        const items = await getComplaintService(req);
        return successResponse(
            reply,
            items.statusCode,
            items.message,
            items.status,
            items.data
        );
    } catch (err) {
        console.error("Error in getComplaint controller:", err);
        return errorResponse(reply, 500, err, {}, "error", req);
    }
};

export const addComplaint = async (req, reply) => {
    try {
        const result = await addComplaintService(req);
        console.log("result", result);
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, {}, "error", req);
    }
};

export const myOrders = async (req, reply) => {
    try {
        const result = await myOrdersService(req);

        if (result === "error") {
            return reply.code(500).send({
                status: "error",
                message: "Error fetching orders",
                data: {},
            });
        }

        // Handle authentication error
        if (result.statusCode === 401) {
            return reply.code(401).send({
                status: "error",
                message: result.message,
                data: {},
            });
        }

        return successResponse(
            reply,
            result.statusCode || 200,
            result.message || "Orders fetched successfully",
            result.status ? "success" : "failure",
            result.data || result
        );
    } catch (err) {
        console.error("Error in myOrders controller:", err);
        return errorResponse(reply, 500, err, {}, "error", req);
    }
};

export const addFeed = async (req, reply) => {
    try {
        const result = await addFeedService(req);
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status ? "success" : "failure",
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, {}, "error", req);
    }
};

export const checkOrder = async (req, reply) => {
    try {
        const result = await checkOrderService(req);
        return reply.code(result.statusCode).send({
            message: result.message,
            status: result.status ? "success" : "failure",
            data: result.data,
        });
    } catch (err) {
        return errorResponse(reply, 500, err, {}, "error", req);
    }
};

export const trackYourOrder = async (req, reply) => {
    try {
        const result = await trackYourOrderService(req);
        return reply.code(result.statusCode).send({
            message: result.message,
            status: result.status ? "success" : "failure",
            data: result.data,
        });
    } catch (err) {
        return errorResponse(reply, 500, err, {}, "error", req);
    }
};

export const getOrderByRefId = async (req, reply) => {
    try {
        const result = await getOrderByRefIdService(req);
        return reply.code(result.statusCode).send({
            message: result.message,
            status: result.status ? "success" : "failure",
            data: result.data,
        });
    } catch (err) {
        return errorResponse(reply, 500, err, {}, "error", req);
    }
}
